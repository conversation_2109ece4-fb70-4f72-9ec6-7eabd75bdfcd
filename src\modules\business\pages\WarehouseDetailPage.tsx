import React, { useState, useMemo, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Loading,
  Table,
  IconCard,
  Tooltip,
  Modal,
  Input,
} from '@/shared/components';
import { TableColumn } from '@/shared/components/common/Table/types';
import { useWarehouse } from '../hooks/useWarehouseQuery';
import { useInventoryItemsByWarehouse, useUpdateInventoryQuantity } from '../hooks/useInventoryQuery';
import { InventoryItemDto, UpdateInventoryQuantityDto, InventoryQueryParams } from '../types/inventory.types';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';

/**
 * Interface cho props của component
 */
interface WarehouseDetailPageProps {
  warehouseId?: string;
}

/**
 * Interface cho form cập nhật số lượng
 */
interface UpdateQuantityFormData {
  availableQuantity: number;
  reservedQuantity: number;
  defectiveQuantity: number;
}

/**
 * Trang chi tiết kho với danh sách sản phẩm
 */
const WarehouseDetailPage: React.FC<WarehouseDetailPageProps> = () => {
  const { warehouseId } = useParams<{ warehouseId: string }>();
  const { t } = useTranslation(['business', 'common']);

  // State
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  const [selectedInventoryItem, setSelectedInventoryItem] = useState<InventoryItemDto | null>(null);
  const [updateForm, setUpdateForm] = useState<UpdateQuantityFormData>({
    availableQuantity: 0,
    reservedQuantity: 0,
    defectiveQuantity: 0,
  });

  // Parse warehouseId
  const warehouseIdNum = warehouseId ? parseInt(warehouseId) : 0;

  // Queries
  const { data: warehouseResponse, isLoading: warehouseLoading } = useWarehouse(warehouseIdNum);
  const warehouse = warehouseResponse?.result;

  // Xử lý mở modal cập nhật số lượng
  const handleUpdateQuantity = useCallback((item: InventoryItemDto) => {
    setSelectedInventoryItem(item);
    setUpdateForm({
      availableQuantity: item.availableQuantity || 0,
      reservedQuantity: item.reservedQuantity || 0,
      defectiveQuantity: item.defectiveQuantity || 0,
    });
    setIsUpdateModalOpen(true);
  }, []);

  // Định nghĩa cột cho bảng sản phẩm
  const columns: TableColumn<InventoryItemDto>[] = useMemo(
    () => [
      {
        key: 'productName',
        title: t('business:product.name'),
        render: (_, record) => (
          <div className="flex items-center space-x-3">
            {record.product?.images?.[0] && (
              <img
                src={record.product.images[0].url}
                alt={record.product.name}
                className="w-10 h-10 rounded object-cover"
              />
            )}
            <div>
              <Typography variant="body2" className="font-medium">
                {record.product?.name || 'N/A'}
              </Typography>
              {record.product?.code && (
                <Typography variant="caption" className="text-gray-500">
                  {record.product.code}
                </Typography>
              )}
            </div>
          </div>
        ),
        sortable: false,
      },
      {
        key: 'productCode',
        title: t('business:product.code'),
        render: (_, record) => record.product?.code || 'N/A',
        sortable: false,
      },
      {
        key: 'currentQuantity',
        title: t('business:inventory.currentQuantity'),
        dataIndex: 'currentQuantity',
        render: (value: unknown) => (
          <Typography variant="body2" className="font-medium">
            {(value as number) || 0}
          </Typography>
        ),
        sortable: true,
      },
      {
        key: 'availableQuantity',
        title: t('business:inventory.availableQuantity'),
        dataIndex: 'availableQuantity',
        render: (value: unknown) => (value as number) || 0,
        sortable: true,
      },
      {
        key: 'reservedQuantity',
        title: t('business:inventory.reservedQuantity'),
        dataIndex: 'reservedQuantity',
        render: (value: unknown) => (value as number) || 0,
        sortable: true,
      },
      {
        key: 'defectiveQuantity',
        title: t('business:inventory.defectiveQuantity'),
        dataIndex: 'defectiveQuantity',
        render: (value: unknown) => (value as number) || 0,
        sortable: true,
      },
      {
        key: 'actions',
        title: t('common:actions'),
        render: (_, record) => (
          <div className="flex space-x-2">
            <Tooltip content={t('business:inventory.updateQuantity')}>
              <IconCard
                icon="edit"
                variant="ghost"
                size="sm"
                onClick={() => handleUpdateQuantity(record)}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handleUpdateQuantity]
  );

  // Tạo hàm createQueryParams cho inventory
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => {
    return {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
      warehouseId: warehouseIdNum,
    };
  };

  // Sử dụng hook useDataTable
  const dataTable = useDataTable(
    useDataTableConfig<InventoryItemDto, Record<string, unknown>>({
      columns,
      filterOptions: [],
      createQueryParams,
    })
  );

  // Tạo query params cho inventory API (loại bỏ warehouseId vì đã được truyền riêng)
  const inventoryQueryParams = useMemo(() => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { warehouseId, ...restParams } = dataTable.queryParams;
    return restParams as Omit<InventoryQueryParams, 'warehouseId'>;
  }, [dataTable.queryParams]);

  // Lấy danh sách sản phẩm trong kho
  const { data: inventoryData, isLoading: inventoryLoading } = useInventoryItemsByWarehouse(
    warehouseIdNum,
    inventoryQueryParams
  );

  // Mutation để cập nhật số lượng
  const { mutateAsync: updateQuantity, isPending: isUpdating } = useUpdateInventoryQuantity();

  // Xử lý đóng modal
  const handleCloseModal = useCallback(() => {
    setIsUpdateModalOpen(false);
    setSelectedInventoryItem(null);
    setUpdateForm({
      availableQuantity: 0,
      reservedQuantity: 0,
      defectiveQuantity: 0,
    });
  }, []);

  // Xử lý submit form cập nhật
  const handleSubmitUpdate = useCallback(async () => {
    if (!selectedInventoryItem) return;

    try {
      const updateData: UpdateInventoryQuantityDto = {
        availableQuantity: updateForm.availableQuantity,
        reservedQuantity: updateForm.reservedQuantity,
        defectiveQuantity: updateForm.defectiveQuantity,
      };

      await updateQuantity({
        id: selectedInventoryItem.id,
        data: updateData,
      });

      handleCloseModal();
    } catch (error) {
      console.error('Error updating quantity:', error);
    }
  }, [selectedInventoryItem, updateForm, updateQuantity, handleCloseModal]);

  // Loading state
  if (warehouseLoading) {
    return <Loading />;
  }

  // Error state
  if (!warehouse) {
    return (
      <div className="flex items-center justify-center h-64">
        <Typography variant="body1" className="text-gray-500">
          {t('business:warehouse.notFound')}
        </Typography>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h4" className="mt-2">
            {warehouse.name}
          </Typography>
          {warehouse.description && (
            <Typography variant="body2" className="text-gray-600 mt-1">
              {warehouse.description}
            </Typography>
          )}
        </div>
      </div>

      {/* Warehouse Info */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Typography variant="subtitle2" className="text-gray-500">
              {t('business:warehouse.type')}
            </Typography>
            <Typography variant="body1">
              {warehouse.type ? t(`business:warehouse.types.${warehouse.type}`) : t('common:notSet')}
            </Typography>
          </div>
          <div>
            <Typography variant="subtitle2" className="text-gray-500">
              {t('business:warehouse.status')}
            </Typography>
            <Typography variant="body1">
              {warehouse.status || t('common:notSet')}
            </Typography>
          </div>
          <div>
            <Typography variant="subtitle2" className="text-gray-500">
              {t('business:inventory.totalProducts')}
            </Typography>
            <Typography variant="body1" className="font-medium">
              {inventoryData?.meta.totalItems || 0}
            </Typography>
          </div>
        </div>
      </Card>

      {/* Menu Bar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}

      />

      {/* Products Table */}
      <Card className="overflow-hidden">
        {inventoryData?.items && inventoryData.items.length > 0 ? (
          <Table
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={inventoryData.items}
            rowKey="id"
            loading={inventoryLoading}
            sortable={true}
            onSortChange={dataTable.tableData.handleSortChange}
            pagination={{
              current: inventoryData?.meta.currentPage || 1,
              pageSize: dataTable.tableData.pageSize,
              total: inventoryData?.meta.totalItems || 0,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        ) : !inventoryLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <Icon name="package" className="w-16 h-16 text-gray-300 mb-4" />
            <Typography variant="h6" className="text-gray-500 mb-2">
              {t('business:inventory.noProducts')}
            </Typography>
            <Typography variant="body2" className="text-gray-400 text-center">
              {t('business:inventory.noProductsDescription')}
            </Typography>
          </div>
        ) : (
          <div className="py-12">
            <Loading />
          </div>
        )}
      </Card>

      {/* Update Quantity Modal */}
      <Modal
        isOpen={isUpdateModalOpen}
        onClose={handleCloseModal}
        title={t('business:inventory.updateQuantity')}
        size="md"
      >
        <div className="space-y-4">
          {selectedInventoryItem && (
            <div className="mb-4">
              <Typography variant="body2" className="text-gray-600">
                {t('business:product.name')}: {selectedInventoryItem.product?.name}
              </Typography>
              {selectedInventoryItem.product?.code && (
                <Typography variant="caption" className="text-gray-500">
                  {t('business:product.code')}: {selectedInventoryItem.product.code}
                </Typography>
              )}
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('business:inventory.availableQuantity')}
            </label>
            <Input
              type="number"
              value={updateForm.availableQuantity}
              onChange={(e) => setUpdateForm(prev => ({
                ...prev,
                availableQuantity: parseInt(e.target.value) || 0
              }))}
              min={0}
              placeholder="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('business:inventory.reservedQuantity')}
            </label>
            <Input
              type="number"
              value={updateForm.reservedQuantity}
              onChange={(e) => setUpdateForm(prev => ({
                ...prev,
                reservedQuantity: parseInt(e.target.value) || 0
              }))}
              min={0}
              placeholder="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('business:inventory.defectiveQuantity')}
            </label>
            <Input
              type="number"
              value={updateForm.defectiveQuantity}
              onChange={(e) => setUpdateForm(prev => ({
                ...prev,
                defectiveQuantity: parseInt(e.target.value) || 0
              }))}
              min={0}
              placeholder="0"
            />
          </div>

          {/* Hiển thị tổng số lượng tính toán */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <Typography variant="body2" className="text-gray-600 mb-1">
              {t('business:inventory.totalQuantity')}
            </Typography>
            <Typography variant="h6" className="font-semibold">
              {updateForm.availableQuantity + updateForm.reservedQuantity + updateForm.defectiveQuantity}
            </Typography>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={handleCloseModal}
              disabled={isUpdating}
            >
              {t('common:cancel')}
            </Button>
            <Button
              onClick={handleSubmitUpdate}
              isLoading={isUpdating}
            >
              {t('common:save')}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default WarehouseDetailPage;
