import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { UserKeyLlmService } from '../services/user-key-llm.service';
import {
  UserKeyLlmQueryDto,
  CreateUserKeyLlmDto,
  UpdateUserKeyLlmDto,
  TestKeyDto,
} from '../types/user-key-llm.types';

/**
 * Interface cho API error response
 */
interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
  message?: string;
}

/**
 * Query keys cho User Key LLM
 */
export const userKeyLlmKeys = {
  all: ['user-key-llm'] as const,
  lists: () => [...userKeyLlmKeys.all, 'list'] as const,
  list: (params: UserKeyLlmQueryDto) => [...userKeyLlmKeys.lists(), params] as const,
  details: () => [...userKeyLlmKeys.all, 'detail'] as const,
  detail: (id: string) => [...userKeyLlmKeys.details(), id] as const,
  usageStats: (id: string) => [...userKeyLlmKeys.all, 'usage-stats', id] as const,
  quotaInfo: (id: string) => [...userKeyLlmKeys.all, 'quota', id] as const,
  models: (provider: string) => [...userKeyLlmKeys.all, 'models', provider] as const,
};

/**
 * Hook để lấy danh sách key LLM
 */
export const useUserKeyLlmList = (params: UserKeyLlmQueryDto) => {
  return useQuery({
    queryKey: userKeyLlmKeys.list(params),
    queryFn: () => UserKeyLlmService.getList(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết key LLM
 */
export const useUserKeyLlmDetail = (id: string, enabled = true) => {
  return useQuery({
    queryKey: userKeyLlmKeys.detail(id),
    queryFn: () => UserKeyLlmService.getById(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy thống kê sử dụng
 */
export const useUserKeyLlmUsageStats = (id: string, enabled = true) => {
  return useQuery({
    queryKey: userKeyLlmKeys.usageStats(id),
    queryFn: () => UserKeyLlmService.getUsageStats(id),
    enabled: enabled && !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook để lấy thông tin quota
 */
export const useUserKeyLlmQuotaInfo = (id: string, enabled = true) => {
  return useQuery({
    queryKey: userKeyLlmKeys.quotaInfo(id),
    queryFn: () => UserKeyLlmService.getQuotaInfo(id),
    enabled: enabled && !!id,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

/**
 * Hook để lấy danh sách models
 */
export const useAvailableModels = (provider: string, enabled = true) => {
  return useQuery({
    queryKey: userKeyLlmKeys.models(provider),
    queryFn: () => UserKeyLlmService.getAvailableModels(provider),
    enabled: enabled && !!provider,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook để tạo key LLM
 */
export const useCreateUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserKeyLlmDto) => UserKeyLlmService.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      toast.success('Tạo key LLM thành công');
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi tạo key LLM');
    },
  });
};

/**
 * Hook để cập nhật key LLM
 */
export const useUpdateUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserKeyLlmDto }) =>
      UserKeyLlmService.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.detail(id) });
      toast.success('Cập nhật key LLM thành công');
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật key LLM');
    },
  });
};

/**
 * Hook để xóa key LLM
 */
export const useDeleteUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => UserKeyLlmService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      toast.success('Xóa key LLM thành công');
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi xóa key LLM');
    },
  });
};

/**
 * Hook để test key LLM
 */
export const useTestUserKeyLlm = () => {
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data?: TestKeyDto }) =>
      UserKeyLlmService.testKey(id, data),
    onSuccess: result => {
      if (result.success) {
        toast.success('Test key thành công');
      } else {
        toast.error(`Test key thất bại: ${result.message}`);
      }
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi test key');
    },
  });
};

/**
 * Hook để kích hoạt key
 */
export const useActivateUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => UserKeyLlmService.activate(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.detail(id) });
      toast.success('Kích hoạt key thành công');
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi kích hoạt key');
    },
  });
};

/**
 * Hook để vô hiệu hóa key
 */
export const useDeactivateUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => UserKeyLlmService.deactivate(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.detail(id) });
      toast.success('Vô hiệu hóa key thành công');
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi vô hiệu hóa key');
    },
  });
};

/**
 * Hook để làm mới key
 */
export const useRegenerateUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => UserKeyLlmService.regenerate(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.detail(id) });
      toast.success('Làm mới key thành công');
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi làm mới key');
    },
  });
};

/**
 * Hook để nhân bản key
 */
export const useDuplicateUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) =>
      UserKeyLlmService.duplicate(id, name),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      toast.success('Nhân bản key thành công');
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi nhân bản key');
    },
  });
};

/**
 * Hook để validate key format
 */
export const useValidateKeyFormat = () => {
  return useMutation({
    mutationFn: ({ provider, apiKey }: { provider: string; apiKey: string }) =>
      UserKeyLlmService.validateKeyFormat(provider, apiKey),
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi validate key');
    },
  });
};

/**
 * Hook để batch delete keys
 */
export const useBatchDeleteUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => UserKeyLlmService.batchDelete(ids),
    onSuccess: result => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      toast.success(`Đã xóa ${result.deleted} key(s) thành công`);
      if (result.failed > 0) {
        toast.error(`${result.failed} key(s) xóa thất bại`);
      }
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi xóa keys');
    },
  });
};

/**
 * Hook để batch activate keys
 */
export const useBatchActivateUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => UserKeyLlmService.batchActivate(ids),
    onSuccess: result => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      toast.success(`Đã kích hoạt ${result.activated} key(s) thành công`);
      if (result.failed > 0) {
        toast.error(`${result.failed} key(s) kích hoạt thất bại`);
      }
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi kích hoạt keys');
    },
  });
};

/**
 * Hook để batch deactivate keys
 */
export const useBatchDeactivateUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => UserKeyLlmService.batchDeactivate(ids),
    onSuccess: result => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      toast.success(`Đã vô hiệu hóa ${result.deactivated} key(s) thành công`);
      if (result.failed > 0) {
        toast.error(`${result.failed} key(s) vô hiệu hóa thất bại`);
      }
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi vô hiệu hóa keys');
    },
  });
};

/**
 * Hook để export keys
 */
export const useExportUserKeyLlm = () => {
  return useMutation({
    mutationFn: () => UserKeyLlmService.exportKeys(),
    onSuccess: blob => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `user-keys-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success('Export keys thành công');
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi export keys');
    },
  });
};

/**
 * Hook để import keys
 */
export const useImportUserKeyLlm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => UserKeyLlmService.importKeys(file),
    onSuccess: result => {
      queryClient.invalidateQueries({ queryKey: userKeyLlmKeys.lists() });
      toast.success(`Import thành công ${result.imported} key(s)`);
      if (result.failed > 0) {
        toast.error(`${result.failed} key(s) import thất bại`);
      }
    },
    onError: (error: ApiError) => {
      toast.error(error?.response?.data?.message || 'Có lỗi xảy ra khi import keys');
    },
  });
};
