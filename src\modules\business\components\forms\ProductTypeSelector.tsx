import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, IconCard } from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import { Package, Download, Wrench, Calendar, Package2 } from 'lucide-react';
import { ProductTypeEnum } from '../../types/product.types';
import type { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';

interface ProductTypeOption {
  type: ProductTypeEnum;
  title: string;
  description: string;
  icon: typeof Package;
  color: 'blue' | 'green' | 'orange' | 'purple' | 'red' | 'gray';
}

interface ProductTypeSelectorProps {
  /**
   * Callback khi chọn loại sản phẩm
   */
  onTypeSelect: (type: ProductTypeEnum) => void;

  /**
   * Callback khi hủy
   */
  onCancel: () => void;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component cho phép người dùng chọn loại sản phẩm
 */
const ProductTypeSelector: React.FC<ProductTypeSelectorProps> = ({
  onTypeSelect,
  onCancel,
  className = '',
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Định nghĩa các loại sản phẩm với thông tin hiển thị
  const productTypes: ProductTypeOption[] = [
    {
      type: ProductTypeEnum.PHYSICAL,
      title: t('business:product.types.physical.title', 'Sản phẩm vật lý'),
      description: t('business:product.types.physical.description', 'Sản phẩm có thể sờ được, cần vận chuyển'),
      icon: Package,
      color: 'blue',
    },
    {
      type: ProductTypeEnum.DIGITAL,
      title: t('business:product.types.digital.title', 'Sản phẩm số'),
      description: t('business:product.types.digital.description', 'File, khóa học, ebook, phần mềm'),
      icon: Download,
      color: 'purple',
    },
    {
      type: ProductTypeEnum.SERVICE,
      title: t('business:product.types.service.title', 'Dịch vụ'),
      description: t('business:product.types.service.description', 'Tư vấn, làm đẹp, bảo trì, lắp đặt'),
      icon: Wrench,
      color: 'green',
    },
    {
      type: ProductTypeEnum.EVENT,
      title: t('business:product.types.event.title', 'Sự kiện'),
      description: t('business:product.types.event.description', 'Hội thảo, khóa học, buổi biểu diễn'),
      icon: Calendar,
      color: 'orange',
    },
    {
      type: ProductTypeEnum.COMBO,
      title: t('business:product.types.combo.title', 'Combo'),
      description: t('business:product.types.combo.description', 'Gói sản phẩm kết hợp nhiều loại'),
      icon: Package2,
      color: 'red',
    },
  ];

  // Chuyển đổi sang format OverviewCard
  const overviewCardItems: OverviewCardProps[] = productTypes.map((option) => ({
    title: option.title,
    value: '', // Không hiển thị value
    description: option.description,
    icon: option.icon,
    color: option.color,
    hoverable: true,
    onClick: () => onTypeSelect(option.type),
  }));

  return (
    <div className={`w-full bg-background text-foreground ${className}`}>
      {/* Header */}
      <div className="mb-6">
        <Typography variant="h4" className="font-semibold mb-2">
          {t('business:product.typeSelector.title', 'Chọn loại sản phẩm')}
        </Typography>
      </div>

      {/* Product Type Grid using ListOverviewCard */}
      <div className="mb-6">
        <ListOverviewCard
          items={overviewCardItems}
          maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 4 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }}
          gap={4}
        />
      </div>

      {/* Actions */}
      <div className="flex justify-end">
        <IconCard
          icon="x"
          variant="secondary"
          size="md"
          title={t('common:cancel', 'Hủy')}
          onClick={onCancel}
        />
      </div>
    </div>
  );
};

export default ProductTypeSelector;
