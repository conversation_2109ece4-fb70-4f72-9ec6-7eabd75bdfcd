import React from 'react';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import AdminAgentCard from './AdminAgentCard';
import { AgentSystemListItem } from '../agent-system/types/agent-system.types';

interface AdminAgentGridProps {
  agents: AgentSystemListItem[];
}

/**
 * Component hiển thị danh sách Admin Agents dưới dạng grid
 */
const AdminAgentGrid: React.FC<AdminAgentGridProps> = ({ agents }) => {
  return (
    <ResponsiveGrid
      maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
      maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
      gap={6}
    >
      {agents.map((agent) => (
        <AdminAgentCard key={agent.id} agent={agent} />
      ))}
    </ResponsiveGrid>
  );
};

export default AdminAgentGrid;
