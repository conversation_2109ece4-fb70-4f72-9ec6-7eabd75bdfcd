import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination } from '@/shared/components/common';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AdminAgentGrid } from '../components';
import { useAdminAgentSystems } from '../agent-system/hooks/useAgentSystem';

/**
 * Trang hiển thị danh sách System Agents
 */
const AgentSystemPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(12);
  const [search, setSearch] = useState('');

  // Query params
  const queryParams = {
    page,
    limit,
    search: search || undefined,
  };

  // <PERSON><PERSON><PERSON> sách system agents
  const { data: agentsResponse, isLoading, error, refetch } = useAdminAgentSystems(queryParams);

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleAddAgent = () => {
    navigate('/admin/agent/system/add');
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Lấy danh sách agents từ response
  const agents = agentsResponse?.result?.items || [];

  const totalItems = agentsResponse?.result?.meta?.totalItems || 0;

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={[]}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddAgent}
          items={[]}
        />
        <EmptyState
          icon="alert-circle"
          title={t('common.error', 'Lỗi')}
          description={t('admin:agent.list.loadError')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common.retry', 'Thử lại')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddAgent}
        items={[]}
      />

      {agents.length > 0 ? (
        <>
          <AdminAgentGrid agents={agents} />

          {/* Pagination */}
          {totalItems > limit && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={totalItems}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[6, 12, 24, 48]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="cpu"
          title={t('admin:agent.list.noAgents')}
          description={
            search
              ? t('common.noSearchResults', 'Không tìm thấy kết quả phù hợp với từ khóa tìm kiếm.')
              : t('admin:agent.list.noAgentsDescription')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleAddAgent}
            >
              {t('common.add', 'Thêm mới')}
            </Button>
          }
        />
      )}
    </div>
  );
};

export default AgentSystemPage;
